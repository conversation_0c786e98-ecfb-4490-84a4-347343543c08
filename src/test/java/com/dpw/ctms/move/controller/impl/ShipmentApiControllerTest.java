package com.dpw.ctms.move.controller.impl;

import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.utils.Faker;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ShipmentApiControllerTest {

    @Mock
    private IShipmentService shipmentService;

    @InjectMocks
    private ShipmentApiController shipmentApiController;

    private ShipmentListingRequest request;
    private ListResponse<ShipmentListingResponse> response;

    @BeforeEach
    void setUp() {
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        request = Faker.createShipmentListingRequestWithFilter(filter);
        response = ListResponse.<ShipmentListingResponse>builder()
                .totalRecords(1L)
                .data(Collections.singletonList(
                        ShipmentListingResponse.builder()
                                .code("SHIP001")
                                .build()
                ))
                .build();
    }

    @Test
    void listShipments_WithValidRequest_ShouldReturnOk() {
        // Arrange
        when(shipmentService.listShipments(request)).thenReturn(response);

        // Act
        ResponseEntity<ListResponse<ShipmentListingResponse>> result =
                shipmentApiController.listShipments(request);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertNotNull(result.getBody());
        assertEquals(1L, result.getBody().getTotalRecords());
        assertEquals(1, result.getBody().getData().size());
        verify(shipmentService).listShipments(request);
    }

    @Test
    void listShipments_WithNullRequest_ShouldHandleGracefully() {
        // Arrange
        ListResponse<ShipmentListingResponse> emptyResponse = ListResponse.<ShipmentListingResponse>builder()
                .totalRecords(0L)
                .data(Collections.emptyList())
                .build();
        when(shipmentService.listShipments(null)).thenReturn(emptyResponse);

        // Act
        ResponseEntity<ListResponse<ShipmentListingResponse>> result =
                shipmentApiController.listShipments(null);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertNotNull(result.getBody());
        assertEquals(0L, result.getBody().getTotalRecords());
        assertTrue(result.getBody().getData().isEmpty());
        verify(shipmentService).listShipments(null);
    }

    @Test
    void listShipments_WithServiceException_ShouldPropagateException() {
        // Arrange
        when(shipmentService.listShipments(request))
                .thenThrow(new RuntimeException("Service error"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class,
            () -> shipmentApiController.listShipments(request));
        assertEquals("Service error", exception.getMessage());
        verify(shipmentService).listShipments(request);
    }

    @Test
    void listShipments_WithEmptyResponse_ShouldReturnOk() {
        // Arrange
        ListResponse<ShipmentListingResponse> emptyResponse = ListResponse.<ShipmentListingResponse>builder()
                .totalRecords(0L)
                .data(Collections.emptyList())
                .build();
        when(shipmentService.listShipments(request)).thenReturn(emptyResponse);

        // Act
        ResponseEntity<ListResponse<ShipmentListingResponse>> result =
                shipmentApiController.listShipments(request);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertNotNull(result.getBody());
        assertEquals(0L, result.getBody().getTotalRecords());
        assertTrue(result.getBody().getData().isEmpty());
        verify(shipmentService).listShipments(request);
    }
}
