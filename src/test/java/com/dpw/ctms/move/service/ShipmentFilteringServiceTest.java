package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.mapper.ShipmentMapper;
import com.dpw.ctms.move.repository.ShipmentRepository;
import com.dpw.ctms.move.request.DateRange;
import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import com.dpw.ctms.move.utils.Faker;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ShipmentFilteringServiceTest {

    @Mock
    private ShipmentRepository shipmentRepository;

    @Mock
    private ShipmentMapper shipmentMapper;

    @InjectMocks
    private ShipmentFilteringService shipmentFilteringService;

    @Captor
    private ArgumentCaptor<Pageable> pageableCaptor;

    private ShipmentListingRequest request;
    private Shipment shipment;
    private ShipmentListingResponse response;

    @BeforeEach
    void setUp() {
        Pagination pagination = new Pagination();
        pagination.setPageNo(0);
        pagination.setPageSize(10);

        Sort sort = new Sort();
        sort.setSortBy("code");
        sort.setSortOrder("ASC");

        request = ShipmentListingRequest.builder()
                .pagination(pagination)
                .sort(sort)
                .filter(new ShipmentListingRequest.Filter())
                .build();

        shipment = Shipment.builder()
                .code("SHIP001")
                .status(ShipmentStatus.ASSIGNED)
                .build();

        response = ShipmentListingResponse.builder()
                .code("SHIP001")
                .build();
    }

    @Test
    void filterShipments_WithValidRequest_ShouldReturnResults() {
        // Arrange
        Page<Shipment> page = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);
        when(shipmentMapper.toResponse(shipment)).thenReturn(response);

        // Act
        ListResponse<ShipmentListingResponse> result = shipmentFilteringService.filterShipments(request);

        // Assert
        assertNotNull(result);
        assertEquals(1L, result.getTotalRecords());
        assertEquals(1, result.getData().size());
        verify(shipmentRepository).findAll(any(Specification.class), any(Pageable.class));
        verify(shipmentMapper).toResponse(shipment);
    }

    @Test
    void filterShipments_WithNullFilter_ShouldWork() {
        // Arrange
        Pagination pagination = new Pagination();
        pagination.setPageNo(0);
        pagination.setPageSize(10);

        Sort sort = new Sort();
        sort.setSortBy("code");
        sort.setSortOrder("ASC");

        ShipmentListingRequest requestWithNullFilter = ShipmentListingRequest.builder()
                .pagination(pagination)
                .sort(sort)
                .filter(null)
                .build();

        Page<Shipment> page = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(10), 0);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        // Act
        ListResponse<ShipmentListingResponse> result = shipmentFilteringService.filterShipments(requestWithNullFilter);

        // Assert
        assertNotNull(result);
        assertEquals(0L, result.getTotalRecords());
        assertTrue(result.getData().isEmpty());
    }

    @Test
    void filterShipments_WithException_ShouldThrowRuntimeException() {
        // Arrange
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> shipmentFilteringService.filterShipments(request));
        assertEquals("Failed to filter shipments", exception.getMessage());
    }

    @Test
    void filterShipments_WithShipmentIds_ShouldFilterCorrectly() {
        // Arrange
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setShipmentIds(Arrays.asList("SHIP001"));

        Pagination pagination = new Pagination();
        pagination.setPageNo(0);
        pagination.setPageSize(10);

        Sort sort = new Sort();
        sort.setSortBy("code");
        sort.setSortOrder("ASC");

        ShipmentListingRequest requestWithFilter = ShipmentListingRequest.builder()
                .pagination(pagination)
                .sort(sort)
                .filter(filter)
                .build();

        Page<Shipment> page = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);
        when(shipmentMapper.toResponse(shipment)).thenReturn(response);

        // Act
        ListResponse<ShipmentListingResponse> result = shipmentFilteringService.filterShipments(requestWithFilter);

        // Assert
        assertNotNull(result);
        assertEquals(1L, result.getTotalRecords());
    }

    @Test
    void filterShipments_WithShipmentStatuses_ShouldFilterCorrectly() {
        // Arrange
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setShipmentStatuses(Arrays.asList("ASSIGNED"));

        ShipmentListingRequest requestWithFilter = ShipmentListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("code", "ASC"))
                .filter(filter)
                .build();

        Page<Shipment> page = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);
        when(shipmentMapper.toResponse(shipment)).thenReturn(response);

        // Act
        ListResponse<ShipmentListingResponse> result = shipmentFilteringService.filterShipments(requestWithFilter);

        // Assert
        assertNotNull(result);
        assertEquals(1L, result.getTotalRecords());
    }

    @Test
    void filterShipments_WithDateRange_ShouldFilterCorrectly() {
        // Arrange
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        DateRange dateRange = DateRange.builder().from(1000L).to(2000L).build();
        filter.setExpectedPickupDateRange(dateRange);
        
        Pagination pagination = new Pagination();
        pagination.setPageNo(0);
        pagination.setPageSize(10);
        
        Sort sort = new Sort();
        sort.setSortBy("code");
        sort.setSortOrder("ASC");
        
        ShipmentListingRequest requestWithFilter = ShipmentListingRequest.builder()
                .pagination(pagination)
                .sort(sort)
                .filter(filter)
                .build();

        Page<Shipment> page = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);
        when(shipmentMapper.toResponse(shipment)).thenReturn(response);

        // Act
        ListResponse<ShipmentListingResponse> result = shipmentFilteringService.filterShipments(requestWithFilter);

        // Assert
        assertNotNull(result);
        assertEquals(1L, result.getTotalRecords());
    }

    @Test
    void filterShipments_WithInvalidShipmentIds_ShouldHandleGracefully() {
        // Arrange
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setShipmentIds(Arrays.asList("", null, "   "));
        
        Pagination pagination = new Pagination();
        pagination.setPageNo(0);
        pagination.setPageSize(10);
        
        Sort sort = new Sort();
        sort.setSortBy("code");
        sort.setSortOrder("ASC");
        
        ShipmentListingRequest requestWithFilter = ShipmentListingRequest.builder()
                .pagination(pagination)
                .sort(sort)
                .filter(filter)
                .build();

        Page<Shipment> page = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(10), 0);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        // Act
        ListResponse<ShipmentListingResponse> result = shipmentFilteringService.filterShipments(requestWithFilter);

        // Assert
        assertNotNull(result);
        assertEquals(0L, result.getTotalRecords());
    }

    @Test
    void filterShipments_WithInvalidShipmentStatuses_ShouldHandleGracefully() {
        // Arrange
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setShipmentStatuses(Arrays.asList("INVALID_STATUS"));
        
        Pagination pagination = new Pagination();
        pagination.setPageNo(0);
        pagination.setPageSize(10);
        
        Sort sort = new Sort();
        sort.setSortBy("code");
        sort.setSortOrder("ASC");
        
        ShipmentListingRequest requestWithFilter = ShipmentListingRequest.builder()
                .pagination(pagination)
                .sort(sort)
                .filter(filter)
                .build();

        Page<Shipment> page = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(10), 0);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        // Act
        ListResponse<ShipmentListingResponse> result = shipmentFilteringService.filterShipments(requestWithFilter);

        // Assert
        assertNotNull(result);
        assertEquals(0L, result.getTotalRecords());
    }

    @Test
    void filterShipments_WithLargePageSize_ShouldLimitToMax() {
        // Arrange
        Pagination largePagination = new Pagination();
        largePagination.setPageNo(0);
        largePagination.setPageSize(1000); // Exceeds max of 500
        
        Sort sort = new Sort();
        sort.setSortBy("code");
        sort.setSortOrder("ASC");
        
        ShipmentListingRequest requestWithLargePage = ShipmentListingRequest.builder()
                .pagination(largePagination)
                .sort(sort)
                .filter(new ShipmentListingRequest.Filter())
                .build();

        Page<Shipment> page = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(500), 0);
        when(shipmentRepository.findAll(any(Specification.class), pageableCaptor.capture())).thenReturn(page);

        // Act
        shipmentFilteringService.filterShipments(requestWithLargePage);

        // Assert
        Pageable pageable = pageableCaptor.getValue();
        assertEquals(500, pageable.getPageSize());
    }

    @Test
    void filterShipments_WithNegativePageNumber_ShouldUseZero() {
        // Arrange
        Pagination negativePagination = new Pagination();
        negativePagination.setPageNo(-1);
        negativePagination.setPageSize(10);
        
        Sort sort = new Sort();
        sort.setSortBy("code");
        sort.setSortOrder("ASC");
        
        ShipmentListingRequest requestWithNegativePage = ShipmentListingRequest.builder()
                .pagination(negativePagination)
                .sort(sort)
                .filter(new ShipmentListingRequest.Filter())
                .build();

        Page<Shipment> page = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(10), 0);
        when(shipmentRepository.findAll(any(Specification.class), pageableCaptor.capture())).thenReturn(page);

        // Act
        shipmentFilteringService.filterShipments(requestWithNegativePage);

        // Assert
        Pageable pageable = pageableCaptor.getValue();
        assertEquals(0, pageable.getPageNumber());
    }

    @Test
    void filterShipments_WithInvalidSortField_ShouldUseDefault() {
        // Arrange
        Pagination pagination = new Pagination();
        pagination.setPageNo(0);
        pagination.setPageSize(10);
        
        Sort invalidSort = new Sort();
        invalidSort.setSortBy("invalidField");
        invalidSort.setSortOrder("ASC");
        
        ShipmentListingRequest requestWithInvalidSort = ShipmentListingRequest.builder()
                .pagination(pagination)
                .sort(invalidSort)
                .filter(new ShipmentListingRequest.Filter())
                .build();

        Page<Shipment> page = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(10), 0);
        when(shipmentRepository.findAll(any(Specification.class), pageableCaptor.capture())).thenReturn(page);

        // Act
        shipmentFilteringService.filterShipments(requestWithInvalidSort);

        // Assert
        Pageable pageable = pageableCaptor.getValue();
        assertEquals("createdAt: ASC", pageable.getSort().toString());
    }

    @Test
    void filterShipments_WithInvalidSortOrder_ShouldUseDefault() {
        // Arrange
        Pagination pagination = new Pagination();
        pagination.setPageNo(0);
        pagination.setPageSize(10);

        Sort invalidSort = new Sort();
        invalidSort.setSortBy("code");
        invalidSort.setSortOrder("INVALID_ORDER");

        ShipmentListingRequest requestWithInvalidSort = ShipmentListingRequest.builder()
                .pagination(pagination)
                .sort(invalidSort)
                .filter(new ShipmentListingRequest.Filter())
                .build();

        Page<Shipment> page = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(10), 0);
        when(shipmentRepository.findAll(any(Specification.class), pageableCaptor.capture())).thenReturn(page);

        // Act
        shipmentFilteringService.filterShipments(requestWithInvalidSort);

        // Assert
        Pageable pageable = pageableCaptor.getValue();
        assertEquals("code: DESC", pageable.getSort().toString());
    }

    @Test
    void filterShipments_WithTripCodes_ShouldFilterCorrectly() {
        // Arrange
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setTripCodes(Arrays.asList("TRIP001"));

        ShipmentListingRequest requestWithFilter = Faker.createShipmentListingRequestWithFilter(filter);

        Page<Shipment> page = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);
        when(shipmentMapper.toResponse(shipment)).thenReturn(response);

        // Act
        ListResponse<ShipmentListingResponse> result = shipmentFilteringService.filterShipments(requestWithFilter);

        // Assert
        assertNotNull(result);
        assertEquals(1L, result.getTotalRecords());
    }

    @Test
    void filterShipments_WithConsignmentIds_ShouldFilterCorrectly() {
        // Arrange
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setConsignmentIds(Arrays.asList("CONSIGNMENT001"));

        ShipmentListingRequest requestWithFilter = Faker.createShipmentListingRequestWithFilter(filter);

        Page<Shipment> page = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);
        when(shipmentMapper.toResponse(shipment)).thenReturn(response);

        // Act
        ListResponse<ShipmentListingResponse> result = shipmentFilteringService.filterShipments(requestWithFilter);

        // Assert
        assertNotNull(result);
        assertEquals(1L, result.getTotalRecords());
    }

    @Test
    void filterShipments_WithTransportOrderCodes_ShouldFilterCorrectly() {
        // Arrange
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setTransportOrderCodes(Arrays.asList("TO001"));

        ShipmentListingRequest requestWithFilter = Faker.createShipmentListingRequestWithFilter(filter);

        Page<Shipment> page = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);
        when(shipmentMapper.toResponse(shipment)).thenReturn(response);

        // Act
        ListResponse<ShipmentListingResponse> result = shipmentFilteringService.filterShipments(requestWithFilter);

        // Assert
        assertNotNull(result);
        assertEquals(1L, result.getTotalRecords());
    }

    @Test
    void filterShipments_WithCustomerOrderIds_ShouldFilterCorrectly() {
        // Arrange
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setCustomerOrderIds(Arrays.asList("CO001"));

        ShipmentListingRequest requestWithFilter = Faker.createShipmentListingRequestWithFilter(filter);

        Page<Shipment> page = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);
        when(shipmentMapper.toResponse(shipment)).thenReturn(response);

        // Act
        ListResponse<ShipmentListingResponse> result = shipmentFilteringService.filterShipments(requestWithFilter);

        // Assert
        assertNotNull(result);
        assertEquals(1L, result.getTotalRecords());
    }
}
