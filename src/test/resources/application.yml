spring:
  application:
    name: ctms-move
  datasource:
    url: ****************************************
    username: test
    password: test
    driverClassName: org.postgresql.Driver
    hikari:
      connection-timeout: 30000
      maximum-pool-size: 5
      minimum-idle: 1
      auto-commit: true
      connection-init-sql: SELECT 1
  jpa:
    hibernate:
      ddl-auto: validate
      naming:
        implicit-strategy: "org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl"
        physical-strategy: "org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl"
    show_sql: false
    properties:
      org.hibernate.envers.store_data_at_delete: true
      hibernate:
        order_inserts: true
        order_updates: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          batch_size: 100
          batch_versioned_data: true
          lob:
            non_contextual_creation: true
  security:
    basic:
      enabled: false
  autoconfigure:
    exclude:
      - org.redisson.spring.starter.RedissonAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
  data:
    redis:
      repositories:
        enabled: false

multi-tenant:
  enabled: true
  entity:
    packages: com.dpw.ctms.move.entity
  multi-data-source:
    enabled: false
    configs:
      tenantConfigs:
        - tenantName: CFR
          flyway:
            change-log: db/default/migration,db/cfr/migration

server:
  servlet:
    context-path: /move
  port: 0  # Random port for tests

logging:
  level:
    com.dpw.ctms.move: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG

tms:
  auth:
    enabled: false
  swagger:
    enable: false
  app:
    timezone: UTC
  audit:
    envers:
      enabled: false
  pagination:
    page-no: 0
    page-size: 10

# Disable Kafka for tests
kafka:
  consumer:
    shipment-task-acknowledgement:
      enable: false
    document-upload:
      enable: false
